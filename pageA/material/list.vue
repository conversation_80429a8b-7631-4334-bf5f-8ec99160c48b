<template>
  <view class="page-container">
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="我的物料" :fixed="false"></Navbar>

    <view class="list-container">
      <!-- 自定义列表 -->
      <scroll-view
        scroll-y
        class="scroll-view"
        @scrolltolower="onLoadMore"
        @refresherrefresh="onRefresh"
        :refresher-enabled="true"
        :refresher-threshold="80"
        :refresher-triggered="refresherTriggered"
      >
        <view v-if="listData.length === 0 && !loading" class="empty-state">
          <text class="empty-text">暂无物料记录</text>
        </view>

        <view v-for="(item, index) in listData" :key="item.id || index" class="material-item">
          <view class="material-row">
            <text class="material-label">物料名称：</text>
            <text class="material-value">{{ item.materialName }}</text>
          </view>

          <view class="material-row">
            <text class="material-label">当前状态：</text>
            <view class="status-container">
              <text :class="['status-tag', getStatusClass(item.status)]">
                {{ getStatusText(item.status) }}
              </text>
            </view>
          </view>

          <view class="material-row" v-if="item.status === '0'">
            <text class="material-label">操作：</text>
            <button
              class="apply-btn"
              @click="showApplyConfirm(item)"
            >
              立即领取
            </button>
          </view>
        </view>

        <view v-if="loading" class="loading-state">
          <text class="loading-text">加载中...</text>
        </view>
      </scroll-view>
    </view>

    <!-- 确认领取弹窗 -->
    <u-modal
      :show="showConfirmModal"
      title="确认领取"
      :content="confirmContent"
      :show-cancel-button="true"
      :show-confirm-button="true"
      confirm-text="确认领取"
      cancel-text="取消"
      @confirm="handleConfirmApply"
      @cancel="handleCancelApply"
      :async-close="true"
    />
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import { getMaterialReceiveList, applyMaterial } from '@/api/center/material/material'

export default {
  name: 'MaterialList',
  components: {
    Navbar
  },
  data() {
    return {
      listData: [],
      loading: false,
      refresherTriggered: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      showConfirmModal: false,
      confirmContent: '',
      currentMaterial: null, // 当前要领取的物料
    };
  },
  onShow() {
    this.getList();
  },
  methods: {
    // 获取物料列表
    async getList() {
      try {
        this.loading = true;
        const params = {
          pageNum: this.pagination.page,
          pageSize: this.pagination.pageSize,
        };

        const response = await getMaterialReceiveList(params);

        if (response.code === 200) {
          const newData = response.data || [];

          if (this.pagination.page === 1) {
            this.listData = newData;
          } else {
            this.listData = [...this.listData, ...newData];
          }

          this.pagination.total = response.data.total || 0;
        }
      } catch (error) {
        console.error('获取物料列表失败:', error);
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.refresherTriggered = false;
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '待领取',
        '1': '申请中',
        '2': '已领取'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        '0': 'status-pending',
        '1': 'status-applying',
        '2': 'status-received'
      };
      return classMap[status] || 'status-unknown';
    },

    // 显示领取确认弹窗
    showApplyConfirm(material) {
      this.currentMaterial = material;
      this.confirmContent = `确认要领取物料"${material.materialName}"吗？`;
      this.showConfirmModal = true;
    },

    // 确认领取
    async handleConfirmApply() {
      if (!this.currentMaterial) return;
      
      try {
        const response = await applyMaterial(this.currentMaterial.materialId);
        
        if (response.code === 200) {
          uni.showToast({
            title: '申请提交成功',
            icon: 'success'
          });
          
          // 刷新列表
          this.pagination.page = 1;
          this.getList();
        } else {
          uni.showToast({
            title: response.msg || '申请失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('申请物料失败:', error);
        uni.showToast({
          title: '申请失败，请重试',
          icon: 'none'
        });
      } finally {
        this.showConfirmModal = false;
        this.currentMaterial = null;
      }
    },

    // 取消领取
    handleCancelApply() {
      this.showConfirmModal = false;
      this.currentMaterial = null;
    },

    // 下拉刷新
    onRefresh() {
      this.refresherTriggered = true;
      this.pagination.page = 1;
      this.getList();
    },

    // 加载更多
    onLoadMore() {
      if (this.listData.length < this.pagination.total && !this.loading) {
        this.pagination.page++;
        this.getList();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f3f4f6;
}

.list-container {
  padding: 20rpx;
  height: calc(100vh - 120rpx);
}

.scroll-view {
  height: 100%;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  .empty-text {
    color: #909399;
    font-size: 28rpx;
  }
}

.material-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.material-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.material-label {
  color: #606266;
  font-size: 28rpx;
  min-width: 140rpx;
}

.material-value {
  color: #303133;
  font-size: 28rpx;
  flex: 1;
}

.status-container {
  flex: 1;
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.status-pending {
    background-color: #f0f0f0;
    color: #909399;
  }

  &.status-applying {
    background-color: #fdf6ec;
    color: #f9ae3d;
  }

  &.status-received {
    background-color: #f5fff0;
    color: #5ac725;
  }

  &.status-unknown {
    background-color: #f5f5f5;
    color: #8c8c8c;
  }
}

/* 操作按钮样式 */
.apply-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  background-color: #3c9cff;
  color: #ffffff;

  &:active {
    background-color: #398ade;
  }
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;

  .loading-text {
    color: #909399;
    font-size: 24rpx;
  }
}
</style>
