<template>
  <view>
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="证件申请"></Navbar>
    <view style="padding: 40rpx;">
      <!-- 状态提示 -->
      <view v-if="form.id" class="status-container">
        <view v-if="form.applyStatus === 1" class="status-tip draft">
          <u-icon name="info-circle-fill" color="#909399" size="24"/>
          <text class="status-text">申请草稿，请完善信息后提交</text>
        </view>

        <view v-if="form.applyStatus === 2" class="status-tip reviewing">
          <u-icon name="clock-fill" color="#ff9500" size="24"/>
          <text class="status-text">申请审核中，请耐心等待</text>
        </view>

        <view v-if="form.applyStatus === 3" class="status-tip approved">
          <u-icon name="checkmark-circle-fill" color="#19be6b" size="24"/>
          <text class="status-text">申请已通过</text>
        </view>
      </view>

      <!-- 驳回原因提示 -->
      <view v-if="form.applyStatus === 4 && form.rejectReason" class="reject-alert">
        <u-icon name="close-circle-fill" color="#fa3534" size="32"/>
        <view class="reject-content">
          <text class="reject-title">申请已驳回</text>
          <text class="reject-reason-text">驳回原因：{{ form.rejectReason }}</text>
        </view>
      </view>

      <u--form
          labelPosition="left"
          :model="form"
          :rules="rules"
          ref="uForm"
          labelWidth="160rpx"
      >
        <!-- 是否实操 -->
        <u-form-item label="是否实操" prop="practiceFlag" borderBottom required>
          <u-radio-group v-model="form.practiceFlag" placement="row" :disabled="[2,3].includes(Number(this.form.applyStatus))">
            <u-radio name="Y" label="是"/>
            <u-radio name="N" label="否" :customStyle="{'margin-left': '10px'}"/>
          </u-radio-group>
        </u-form-item>
        <view style="padding: 0 0 24rpx 0;">
          <view class="tips">
            <text>tips：是否实操指是否参加线下实操课程</text>
          </view>
        </view>
        <!-- 申请类型 -->
<!--        <u-form-item label="申请类型" prop="applyType" borderBottom required>-->
<!--          <u-radio-group v-model="form.applyType" placement="row" :disabled="isDisabled" @change="applyTypeChange">-->
<!--            <u-radio name="SELF" label="本人申请"/>-->
<!--            <u-radio name="OTHER" label="替他人申请" :customStyle="{'margin-left': '10px'}"/>-->
<!--          </u-radio-group>-->
<!--        </u-form-item>-->
        <!-- 姓名 -->
        <u-form-item label="姓名" prop="name" borderBottom required>
          <u--input v-model="form.name" placeholder="请输入姓名" border="none" clearable :disabled="isDisabled"/>
        </u-form-item>
        <!-- 证件类型 -->
        <u-form-item label="证件类型" prop="cardType" borderBottom required @click="handleCardTypeClick">
          <u--input v-model="cardTypeText" disabled disabledColor="#ffffff" placeholder="请选择证件类型" border="none"
                    :disabled="isDisabled"/>
          <u-icon slot="right" name="arrow-right"/>
        </u-form-item>
        <!-- 证件号码 -->
        <u-form-item label="证件号码" prop="idCard" borderBottom required>
          <u--input
              v-model="form.idCard"
              placeholder="请输入证件号码"
              border="none"
              @blur="calculateGender"
              clearable
              :disabled="isDisabled"
          ></u--input>
          <u-button
              slot="right"
              size="large"
              type="primary"
              @click="chooseIdCardImage"
              style="margin-left: 16rpx;"
              :disabled="isDisabled"
          >识别
          </u-button>
        </u-form-item>
        <!-- 性别 -->
        <u-form-item label="性别" prop="sex" borderBottom required @click="handleSexClick">
          <u--input v-model="sexText" disabled disabledColor="#ffffff" placeholder="请选择性别" border="none"
                    :disabled="isDisabled"/>
          <u-icon slot="right" name="arrow-right"/>
        </u-form-item>
        <!-- 联系方式 -->
        <u-form-item label="联系方式" prop="phone" borderBottom required>
          <u--input v-model="form.phone" placeholder="请输入联系方式" border="none" clearable
                    :disabled="isDisabled"/>
        </u-form-item>

        <!-- 学历 -->
        <u-form-item label="学历" prop="education" borderBottom required
                     @click="handleEducationClick">
          <u--input v-model="educationText" disabled disabledColor="#ffffff" placeholder="请选择学历" border="none"
                    :disabled="isDisabled"/>
          <u-icon slot="right" name="arrow-right"/>
        </u-form-item>
        <!-- 工作单位 -->
        <u-form-item label="工作单位" prop="company" borderBottom>
          <u--input v-model="form.company" placeholder="请输入工作单位" border="none" clearable :disabled="isDisabled"/>
        </u-form-item>
        <u-form-item label="邮寄方式" prop="receiveType" borderBottom required>
          <u-radio-group v-model="form.receiveType" placement="row" :disabled="isDisabled"
                         @change="receiveTypeChange">
            <u-radio name="SELF" label="自取"/>
            <u-radio name="MAIL" label="邮寄" :customStyle="{'margin-left': '10px'}"/>
          </u-radio-group>
        </u-form-item>
        <!-- 邮寄地址-->
        <u-form-item label="邮寄地址" prop="receiveAddress" borderBottom required>
          <u-input
              v-model="form.receiveAddress"
              placeholder="请输入邮寄地址"
              border="none"
              clearable
              :disabled="isDisabled || form.receiveType === 'SELF'"
              @focus="showPopup = true"
          />
        </u-form-item>
        <!-- 二寸证件照上传 -->
        <u-form-item label="二寸证件照" prop="photoUrl" borderBottom required>
          <view class="photo-upload">
            <view v-if="form.photoUrl" class="preview-container">
              <image :src="form.photoUrl" mode="aspectFill" class="preview-image" @click="previewImage('photoUrl')"/>
              <view class="delete-btn" @click.stop="deletePhoto('photoUrl')">
                <u-icon name="close" color="#ffffff" size="20"/>
              </view>
            </view>
            <view v-else class="upload-placeholder" @click="chooseImage('photoUrl', 'photoId')">
              <u-icon name="camera-fill" size="40" color="#909399"/>
              <text class="upload-text">点击上传</text>
            </view>
          </view>
          <view class="photo-tip">
            <text>照片需为白底二寸证件照</text>
          </view>
        </u-form-item>
        <!-- 二寸证件照提示 -->

        <!-- 海姆利克/心肺复苏上传 -->
        <u-form-item label="海姆利克/心肺复苏实操图" prop="heimlichUrl" borderBottom required>
          <view class="photo-upload">
            <view v-if="form.heimlichUrl" class="preview-container">
              <image :src="form.heimlichUrl" mode="aspectFill" class="preview-image"
                     @click="previewImage('heimlichUrl')"/>
              <view class="delete-btn" @click.stop="deletePhoto('heimlichUrl')">
                <u-icon name="close" color="#ffffff" size="20"/>
              </view>
            </view>
            <view v-else class="upload-placeholder" @click="chooseImage('heimlichUrl', 'heimlichId')">
              <u-icon name="camera-fill" size="40" color="#909399"/>
              <text class="upload-text">点击上传</text>
            </view>
          </view>
        </u-form-item>

        <u-form-item label="考试截图" prop="examUrl" borderBottom required>
          <view class="photo-upload">
            <view v-if="form.examUrl" class="preview-container">
              <image :src="form.examUrl" mode="aspectFill" class="preview-image"
                     @click="previewImage('examUrl')"/>
              <view class="delete-btn" @click.stop="deletePhoto('examUrl')">
                <u-icon name="close" color="#ffffff" size="20"/>
              </view>
            </view>
            <view v-else class="upload-placeholder" @click="chooseImage('examUrl', 'examId')">
              <u-icon name="camera-fill" size="40" color="#909399"/>
              <text class="upload-text">点击上传</text>
            </view>
          </view>
        </u-form-item>
      </u--form>
      <view style="padding: 0 0 24rpx 0;">
        <view class="tips">
          <text>tips：二寸证件照支付宝搜免费电子证件照拍摄（白底）</text>
        </view>
      </view>

    </view>
    <!-- 性别选择器 -->
    <u-action-sheet :show="showGenderPicker" :actions="genderOptions" title="请选择性别"
                    @close="showGenderPicker = false" @select="onGenderSelect"/>
    <!-- 学历选择器 -->
    <u-picker :show="showEducationPicker" :columns="[educationOptions]" keyName="label" @confirm="onEducationConfirm"
              @cancel="showEducationPicker = false" closeOnClickOverlay @close="showEducationPicker = false"/>
    <!-- 证件类型选择器 -->
    <u-picker :show="showCardTypePicker" :columns="[cardTypeOptions]" keyName="label" @confirm="onCardTypeConfirm"
              @cancel="showCardTypePicker = false" closeOnClickOverlay @close="showCardTypePicker = false"/>
    <u-popup
        :show="showPopup"
        mode="bottom"
        :mask="true"
        @close="showPopup = false"
    >
      <UserAddressList
          @close="showPopup = false"
          @address-selected="handleAddressSelected"
      />
    </u-popup>
    <IdCardPreview
        canvas-id="socialEmergencyCanvas"
        :name="form.name"
        :id-card="form.idCard"
        :sex="sexText"
        :photo-path="form.photoUrl"
        :template-bg="templateBgPath"
        :thumbnail="false"
        :preview-scale="1"
        :show-debug="false"
    />
    <IdCardPreview
        canvas-id="heimlichCanvas"
        :name="form.name"
        :id-card="form.idCard"
        :sex="sexText"
        :photo-path="form.photoUrl"
        :template-bg="templateBgPath2"
        :thumbnail="false"
        :preview-scale="1"
        :show-debug="false"
    />
    <view style="padding: 0 0 10px 0;">
      <u-row gutter="32">
        <u-col span="4">
          <u-button text="保存" plain @click="onSave" :disabled="isDisabled"/>
        </u-col>
        <u-col span="4">
          <u-button text="提交" type="primary" @click="onSubmit" :disabled="isDisabled"/>
        </u-col>
        <u-col span="4">
          <u-button text="保存并提交" type="warning" @click="onSaveAndSubmit" :disabled="isDisabled"/>
        </u-col>
      </u-row>
    </view>
  </view>

</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import IdCardPreview from '@/components/id-card-preview/id-card-preview'
import UserAddressList from '@/components/user-address-list/user-address-list'
import {getDicts} from '@/api/system/dict/data'
import {uploadImage} from '@/utils/upload'
import {saveApplyInfo, getApplyInfo, updateApplyInfo, submitApply} from "@/api/center/apply/apply_form"
import storage from '@/utils/storage'

const {ACCESS_TOKEN} = require('@/store/mutation-types')

// 注意 baseUrl、token 获取方式与 personal.vue 保持一致
const {environment} = require('@/config/environment.js')
const baseUrl = environment.baseURL

let app = null;

export default {
  components: {Navbar, IdCardPreview, UserAddressList},
  data() {
    return {
      form: {
        id: '',
        applyType: "SELF",
        name: '',
        cardType: 'IDCARD',
        idCard: '',
        sex: '',
        phone: '',
        education: '',
        company: '',
        receiveType: 'MAIL',
        addressId: '',
        receiveAddress: '',
        practiceFlag: '',
        applyStatus: '1',
        photoUrl: '',
        photoId: '',
        heimlichUrl: '',
        heimlichId: '',
        examUrl: '',
        examId: '',
        rejectReason: '', // 驳回原因
      },
      showPopup: false,
      sexText: '',
      educationText: '',
      cardTypeText: '大陆居民身份证',
      showGenderPicker: false,
      showEducationPicker: false,
      showCardTypePicker: false,
      genderOptions: [
        {name: '男', value: '0'},
        {name: '女', value: '1'}
      ],
      educationOptions: [],
      cardTypeOptions: [],
      rules: {
        practiceFlag: {type: 'string', required: true, message: '请选择是否实操', trigger: ['change']},
        applySelf: {type: 'boolean', required: true, message: '请选择申请类型', trigger: ['change']},
        name: {type: 'string', required: true, message: '请输入姓名', trigger: ['blur']},
        cardType: {type: 'string', required: true, message: '请选择证件类型', trigger: ['change']},
        idCard: [{
          type: 'string',
          required: true,
          message: '请输入证件号码',
          trigger: ['blur']
        }, {
          validator: this.validateIdCard,
          trigger: ['blur']
        }],
        phone: [{type: 'string', required: true, message: '请输入手机号', trigger: ['blur']}, {
          validator: (rule, value, callback) => {
            const reg = /^1[3456789]\d{9}$/
            if (!reg.test(value)) {
              callback(new Error('手机号格式不正确'))
              return
            }
            callback()
          },
        }],
        education: {type: 'string', required: true, message: '请选择学历', trigger: ['change']},
        photoUrl: {type: 'string', required: true, message: '请上传二寸证件照', trigger: ['change']},
        heimlichUrl: {type: 'string', required: true, message: '请上传海姆利克/心肺复苏图片', trigger: ['change']}
      },
      templateBgPath: '/static/asset/emergency_responder.png',
      templateBgPath2: '/static/asset/life_emergency.png'
    }
  },
  onLoad(options) {
    this.$refs.uForm.setRules(this.rules)
    if (options.id) {
      this.form.id = options.id
    }

    // 获取字典数据
    Promise.all([
      getDicts('education_option'),
      getDicts('sys_card_type')
    ]).then(([educationRes, cardTypeRes]) => {
      this.educationOptions = educationRes.data.map(item => ({label: item.dictLabel, value: item.dictValue}))
      this.cardTypeOptions = cardTypeRes.data.map(item => ({label: item.dictLabel, value: item.dictValue}))

      // 设置默认证件类型
      if (this.cardTypeOptions.length > 0) {
        this.form.cardType = this.cardTypeOptions[0].value;
        this.cardTypeText = this.cardTypeOptions[0].label;
      }

      if (this.form.id) {
        this.getApplyInfo()
      }
    }).catch(err => {
      console.error('获取字典数据失败:', err);
    })
  },
  computed: {
    isDisabled() {
      // 只有草稿和已提交状态可以编辑
      let applyStatus = Number(this.form.applyStatus);
      return [2, 3].includes(applyStatus) || this.form.practiceFlag === 'N';
    },
    // 获取申请状态文本
    applyStatusText() {
      const statusMap = {
        '1': '草稿',
        '2': '审核中',
        '3': '已通过',
        '4': '已驳回'
      };
      return statusMap[this.form.applyStatus] || '未知状态';
    }
  },
  created() {
    app = getApp()
    this.fillOwnerInfo()

  },
  methods: {
    getApplyInfo() {
      getApplyInfo(this.form.id).then(res => {
        this.form = res.data
        this.updateSexText()
        this.updateEducationText()
        this.updateCardTypeText()
      }).catch(err => {
        console.log(err)
      })
    },
    hideKeyboard() {
      uni.hideKeyboard()
    },
    handleEducationClick() {
      if (this.form.applyStatus > 1) return
      this.showEducationPicker = true
      this.hideKeyboard()
    },
    handleSexClick() {
      if (this.form.applyStatus > 1) return
      this.showGenderPicker = true
      this.hideKeyboard()
    },
    handleCardTypeClick() {
      if (this.form.applyStatus > 1) return
      this.showCardTypePicker = true
      this.hideKeyboard()
    },
    onGenderSelect(e) {
      this.form.sex = e.value
      this.sexText = e.name
      this.showGenderPicker = false
    },
    onEducationConfirm(e) {
      this.educationText = e.value[0].label
      this.form.education = e.value[0].value
      this.showEducationPicker = false
      this.$refs.uForm.validateField('education')
    },
    onCardTypeConfirm(e) {
      this.cardTypeText = e.value[0].label
      this.form.cardType = e.value[0].value
      this.showCardTypePicker = false
      this.$refs.uForm.validateField('cardType')
    },
    chooseImage(type, idField) {
      if (this.form.applyStatus > 1) return
      uni.chooseImage({
        count: 1,
        success: (res) => {
          const filePath = res.tempFilePaths[0]
          uploadImage(filePath, (url, fileId) => {
            this.form[type] = url
            this.form[idField] = fileId
            this.$refs.uForm.validateField(type)
          }, err => {
            console.log(err)
          });
        }
      })
    },
    previewImage(type) {
      if (this.isDisabled) return
      if (this.form[type]) {
        uni.previewImage({urls: [this.form[type]]})
      }
    },
    deletePhoto(type) {
      if (this.isDisabled) return
      this.form[type] = ''
      this.$refs.uForm.validateField(type)
    },
    chooseIdCardImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          const filePath = res.tempFilePaths[0]
          this.recognizeIdCard(filePath)
        }
      })
    },
    recognizeIdCard(filePath) {
      if (!filePath) return
      uni.showLoading({title: '识别中...'})
      uni.uploadFile({
        url: baseUrl + '/wx/user/idCardInfo',
        filePath: filePath,
        header: {
          'Authorization': 'Bearer ' + storage.get(ACCESS_TOKEN)
        },
        name: 'file',
        success: (res) => {
          uni.hideLoading()
          const result = JSON.parse(res.data)
          if (result.code === 200 && result.data.type === 'Front') {
            const data = result.data
            this.form.name = data.name
            this.form.idCard = data.id
            this.form.sex = data.gender == '男' ? 0 : 1 // 你可以根据需要自动填充性别
            this.sexText = data.gender
            this.$refs.uForm.validateField('idCard')
            this.$refs.uForm.validateField('name')
            this.$refs.uForm.validateField('sex')
            uni.showToast({title: '识别成功', icon: 'success'})
          } else {
            uni.showToast({title: '识别失败', icon: 'error'})
          }
        },
        fail: (err) => {
          uni.hideLoading()
          uni.showToast({title: '识别失败', icon: 'error'})
        }
      })
    },
    calculateGender() {
      // 如果身份证为空，则不进行性别计算
      if (!this.form.idCard) {
        return
      }
      // 只有大陆身份证才自动计算性别
      if (this.form.cardType === 'IDCARD' && this.form.idCard.length === 18) {
        const genderCode = parseInt(this.form.idCard.charAt(16))
        this.form.sex = genderCode % 2 === 1 ? 0 : 1
        this.updateSexText()
        this.$refs.uForm.validateField('sex')
      }
    },
    // 证件号码验证
    validateIdCard(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入证件号码'))
        return
      }

      // 根据证件类型进行不同的校验
      switch (this.form.cardType) {
        case 'IDCARD':
          this.validateMainlandIdCard(value, callback)
          break
        case 'HKM_PASS':
          this.validateHKMacauPass(value, callback)
          break
        case 'TW_PASS':
          this.validateTaiwanPass(value, callback)
          break
        case 'PASSPORT':
          this.validatePassport(value, callback)
          break
        default:
          callback(new Error('不支持的证件类型'))
          break
      }
    },
    // 大陆身份证校验
    validateMainlandIdCard(value, callback) {
      // 大陆身份证号长度校验
      if (value.length !== 18) {
        callback(new Error('大陆身份证号长度必须为18位'))
        return
      }

      // 大陆身份证号格式校验
      const reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
      if (!reg.test(value)) {
        callback(new Error('大陆身份证号格式不正确'))
        return
      }

      // 校验码验证
      const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
      let sum = 0

      for (let i = 0; i < 17; i++) {
        const ai = parseInt(value[i])
        const wi = factor[i]
        sum += ai * wi
      }

      const last = parity[sum % 11]
      if (last !== value[17].toUpperCase()) {
        callback(new Error('大陆身份证号校验码错误'))
        return
      }

      // 自动识别性别
      const genderCode = parseInt(value.charAt(16))
      this.form.sex = genderCode % 2 === 1 ? 0 : 1
      this.updateSexText()
      callback()
    },
    // 港澳居民来往内地通行证校验
    validateHKMacauPass(value, callback) {
      // 港澳通行证格式：H/M + 8位或10位数字
      const hkMacauReg = /^[HMhm]{1}([0-9]{10}|[0-9]{8})$/
      if (!hkMacauReg.test(value)) {
        callback(new Error('港澳居民来往内地通行证格式不正确'))
        return
      }
      callback()
    },
    // 台湾居民来往大陆通行证校验
    validateTaiwanPass(value, callback) {
      // 台胞证格式：8位或10位数字
      const taiwanReg = /^[0-9]{8,10}$/
      if (!taiwanReg.test(value)) {
        callback(new Error('台湾居民来往大陆通行证格式不正确'))
        return
      }
      callback()
    },
    // 护照校验
    validatePassport(value, callback) {
      // 护照格式：字母+数字组合，长度6-20位
      const passportReg = /^[A-Za-z0-9]{6,20}$/
      if (!passportReg.test(value)) {
        callback(new Error('护照号码格式不正确'))
        return
      }
      callback()
    },
    handleAddressSelected(item) {
      this.form.addressId = item.id
      this.form.receiveAddress = item.address
    },
    applyTypeChange(val) {
      console.log("申请类型切换", val)
      if (val === 'SELF') {
        this.fillOwnerInfo()
      } else {
        this.form.name = ''
        this.form.idCard = ''
        this.form.cardType = this.cardTypeOptions.length > 0 ? this.cardTypeOptions[0].value : 'IDCARD'
        this.form.phone = ''
        this.form.company = ''
        this.form.sex = ''
        this.form.education = ''
        this.sexText = ''
        this.educationText = ''
        this.cardTypeText = this.cardTypeOptions.length > 0 ? this.cardTypeOptions[0].label : '大陆居民身份证'
      }
    },
    receiveTypeChange(val) {
      this.form.receiveType = val
      if (val === 'SELF') {
        this.form.receiveAddress = '上海市长宁区协和路1号金庭庄园11号楼'
      } else {
        this.form.receiveAddress = ''
      }
    },
    fillOwnerInfo() {
      // 实现根据用户信息填充表单的逻辑
      let userInfo = app.globalData.realUserInfo
      if (userInfo) {
        this.form.name = userInfo.realName
        this.form.idCard = userInfo.idCard
        this.form.cardType = userInfo.cardType || 'IDCARD'
        this.form.phone = userInfo.phonenumber
        this.form.company = userInfo.company
        this.form.sex = userInfo.sex
        this.form.education = userInfo.education
        this.form.photoUrl = userInfo.photoUrl
        this.form.photoId = userInfo.photoId
        this.updateSexText()
        this.updateEducationText()
        this.updateCardTypeText()
      }
    },
    updateSexText() {
      // 根据性别值更新性别文本 后端传的是带 "0"
      let sex = Number(this.form.sex)
      this.sexText = sex === 0 ? '男' : sex === 1 ? '女' : ''
    },
    updateEducationText() {
      let education = Number(this.form.education)
      this.educationText = this.educationOptions.find(item => Number(item.value) === education)?.label || ''
    },
    updateCardTypeText() {
      const cardTypeOption = this.cardTypeOptions.find(item => item.value === this.form.cardType)
      if (cardTypeOption) {
        this.cardTypeText = cardTypeOption.label
      }
    },
    async handleSave(status) {
      this.$refs.uForm.validate().then(async valid => {
        if (!valid) {
          uni.showToast({title: '请填写必填项', icon: 'none'});
          return;
        }

        this.form.applyStatus = status;

        const api = this.form.id ? updateApplyInfo : saveApplyInfo;

        try {
          const res = await api(this.form);

          // 如果是首次保存，需要获取返回的 id
          if (!this.form.id && res.data) {
            this.form.id = res.data;
          }

          const msg = status === 1 ? '保存成功' : '保存并提交成功';
          uni.showToast({title: msg, icon: 'success'});

          this.getApplyInfo();
        } catch (err) {
          console.error(err);
          uni.showToast({title: err.msg, icon: 'none', duration: 2000});
        }
      });
    },
    async onSubmit() {
      if (this.form.applyStatus > 1) return;
      if (!this.form.id) {
        uni.showToast({title: '请先保存申请', icon: 'none'});
        return;
      }
      this.$refs.uForm.validate().then(async valid => {
        if (!valid) {
          uni.showToast({title: '请填写必填项', icon: 'none'});
          return;
        }
        try {
          await submitApply(this.form.id);
          this.form.applyStatus = 2;
          uni.showToast({title: '提交成功', icon: 'success'});
          uni.navigateTo({url: '/pageA/apply/list'});
        } catch (err) {
          console.error(err);
          uni.showToast({title: err.msg, icon: 'none', duration: 2000});
        }
      });
    },
    onSave() {
      this.handleSave(1); // 保存草稿
    },
    onSaveAndSubmit() {
      this.handleSave(2); // 保存并提交
    }
  }
}
</script>

<style lang="scss" scoped>
.photo-upload {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #dcdfe6;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.upload-text {
  font-size: 24rpx;
  color: #909399;
  margin-top: 10rpx;
}

.photo-tip {
  margin-top: 10rpx;
  padding-left: 24rpx;
}

.photo-tip text {
  font-size: 24rpx;
  color: #faad14;
}

.tips {
  color: #faad14;
  font-size: 24rpx;
  margin-top: 0;
  text-align: center;
}
/* 状态提示样式 */
.status-container {
  margin-bottom: 32rpx;
}

.status-tip {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}

.status-tip .status-text {
  margin-left: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 草稿状态 */
.status-tip.draft {
  background-color: #f4f4f5;
  border: 1rpx solid #e4e7ed;
}

.status-tip.draft .status-text {
  color: #909399;
}

/* 审核中状态 */
.status-tip.reviewing {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
}

.status-tip.reviewing .status-text {
  color: #ff9500;
}

/* 已通过状态 */
.status-tip.approved {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

.status-tip.approved .status-text {
  color: #19be6b;
}

/* 已驳回状态 */
.status-tip.rejected {
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
}

.status-tip.rejected .status-text {
  color: #fa3534;
}

/* 驳回原因提示样式 */
.reject-alert {
  margin: 24rpx 0 16rpx 0;
  padding: 20rpx 24rpx;
  background: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 8rpx;
  display: flex;
  align-items: flex-start;
}

.reject-content {
  flex: 1;
  margin-left: 16rpx;
}

.reject-title {
  display: block;
  font-size: 28rpx;
  color: #fa3534;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.reject-reason-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
}
</style>
